"""
Comprehensive Error Handling System
"""

import traceback
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Type, Union

import structlog

logger = structlog.get_logger(__name__)


class ErrorCategory(Enum):
    """Error categories for classification"""
    NETWORK = "network"
    AUTHENTICATION = "authentication"
    RATE_LIMIT = "rate_limit"
    VALIDATION = "validation"
    PROVIDER_ERROR = "provider_error"
    TIMEOUT = "timeout"
    RESOURCE_EXHAUSTED = "resource_exhausted"
    CONFIGURATION = "configuration"
    UNKNOWN = "unknown"


class ErrorSeverity(Enum):
    """Error severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class ErrorContext:
    """Context information for errors"""
    provider_name: Optional[str] = None
    request_id: Optional[str] = None
    user_id: Optional[str] = None
    operation: Optional[str] = None
    additional_data: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ErrorInfo:
    """Comprehensive error information"""
    error_id: str
    category: ErrorCategory
    severity: ErrorSeverity
    message: str
    original_exception: Optional[Exception] = None
    context: Optional[ErrorContext] = None
    timestamp: datetime = field(default_factory=datetime.now)
    stack_trace: Optional[str] = None
    retry_count: int = 0
    is_retryable: bool = True
    suggested_action: Optional[str] = None
    
    def __post_init__(self):
        if self.original_exception and not self.stack_trace:
            self.stack_trace = traceback.format_exc()


class ErrorClassifier:
    """Classifies errors into categories and determines retry behavior"""
    
    def __init__(self):
        self.classification_rules = {
            # Network errors
            "ConnectionError": (ErrorCategory.NETWORK, ErrorSeverity.MEDIUM, True),
            "TimeoutError": (ErrorCategory.TIMEOUT, ErrorSeverity.MEDIUM, True),
            "ConnectTimeout": (ErrorCategory.TIMEOUT, ErrorSeverity.MEDIUM, True),
            "ReadTimeout": (ErrorCategory.TIMEOUT, ErrorSeverity.MEDIUM, True),
            "HTTPError": (ErrorCategory.NETWORK, ErrorSeverity.MEDIUM, True),
            
            # Authentication errors
            "AuthenticationError": (ErrorCategory.AUTHENTICATION, ErrorSeverity.HIGH, False),
            "PermissionError": (ErrorCategory.AUTHENTICATION, ErrorSeverity.HIGH, False),
            "Unauthorized": (ErrorCategory.AUTHENTICATION, ErrorSeverity.HIGH, False),
            
            # Rate limiting
            "RateLimitError": (ErrorCategory.RATE_LIMIT, ErrorSeverity.MEDIUM, True),
            "TooManyRequests": (ErrorCategory.RATE_LIMIT, ErrorSeverity.MEDIUM, True),
            
            # Validation errors
            "ValidationError": (ErrorCategory.VALIDATION, ErrorSeverity.LOW, False),
            "ValueError": (ErrorCategory.VALIDATION, ErrorSeverity.LOW, False),
            "TypeError": (ErrorCategory.VALIDATION, ErrorSeverity.LOW, False),
            
            # Resource errors
            "ResourceExhausted": (ErrorCategory.RESOURCE_EXHAUSTED, ErrorSeverity.HIGH, True),
            "OutOfMemory": (ErrorCategory.RESOURCE_EXHAUSTED, ErrorSeverity.CRITICAL, False),
        }
        
        self.message_patterns = {
            "rate limit": (ErrorCategory.RATE_LIMIT, ErrorSeverity.MEDIUM, True),
            "quota exceeded": (ErrorCategory.RATE_LIMIT, ErrorSeverity.HIGH, True),
            "timeout": (ErrorCategory.TIMEOUT, ErrorSeverity.MEDIUM, True),
            "connection": (ErrorCategory.NETWORK, ErrorSeverity.MEDIUM, True),
            "unauthorized": (ErrorCategory.AUTHENTICATION, ErrorSeverity.HIGH, False),
            "forbidden": (ErrorCategory.AUTHENTICATION, ErrorSeverity.HIGH, False),
            "invalid api key": (ErrorCategory.AUTHENTICATION, ErrorSeverity.HIGH, False),
            "model not found": (ErrorCategory.CONFIGURATION, ErrorSeverity.HIGH, False),
        }
    
    def classify_error(self, exception: Exception, context: Optional[ErrorContext] = None) -> ErrorInfo:
        """Classify an exception into error information"""
        error_type = type(exception).__name__
        error_message = str(exception).lower()
        
        # Try exact type match first
        if error_type in self.classification_rules:
            category, severity, retryable = self.classification_rules[error_type]
        else:
            # Try message pattern matching
            category, severity, retryable = self._classify_by_message(error_message)
        
        # Generate error ID
        error_id = f"{category.value}_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"
        
        # Determine suggested action
        suggested_action = self._get_suggested_action(category, exception)
        
        return ErrorInfo(
            error_id=error_id,
            category=category,
            severity=severity,
            message=str(exception),
            original_exception=exception,
            context=context,
            is_retryable=retryable,
            suggested_action=suggested_action
        )
    
    def _classify_by_message(self, message: str) -> tuple:
        """Classify error by message patterns"""
        for pattern, classification in self.message_patterns.items():
            if pattern in message:
                return classification
        
        # Default classification
        return ErrorCategory.UNKNOWN, ErrorSeverity.MEDIUM, True
    
    def _get_suggested_action(self, category: ErrorCategory, exception: Exception) -> str:
        """Get suggested action for error category"""
        suggestions = {
            ErrorCategory.NETWORK: "Check network connectivity and try again",
            ErrorCategory.AUTHENTICATION: "Verify API key and permissions",
            ErrorCategory.RATE_LIMIT: "Wait before retrying or reduce request rate",
            ErrorCategory.TIMEOUT: "Increase timeout or try with smaller request",
            ErrorCategory.VALIDATION: "Check request parameters and format",
            ErrorCategory.CONFIGURATION: "Verify provider configuration and model availability",
            ErrorCategory.RESOURCE_EXHAUSTED: "Wait for resources to become available",
            ErrorCategory.PROVIDER_ERROR: "Check provider status and documentation",
        }
        
        return suggestions.get(category, "Review error details and try again")


class ErrorHandler:
    """Comprehensive error handler with logging and metrics"""
    
    def __init__(self):
        self.classifier = ErrorClassifier()
        self.error_history: List[ErrorInfo] = []
        self.error_callbacks: Dict[ErrorCategory, List[Callable]] = {}
        self.logger = logger.bind(component="error_handler")
        self.max_history_size = 1000
    
    def handle_error(
        self,
        exception: Exception,
        context: Optional[ErrorContext] = None,
        notify_callbacks: bool = True
    ) -> ErrorInfo:
        """Handle an error with comprehensive processing"""
        # Classify the error
        error_info = self.classifier.classify_error(exception, context)
        
        # Add to history
        self._add_to_history(error_info)
        
        # Log the error
        self._log_error(error_info)
        
        # Notify callbacks if enabled
        if notify_callbacks:
            self._notify_callbacks(error_info)
        
        return error_info
    
    def register_callback(self, category: ErrorCategory, callback: Callable[[ErrorInfo], None]):
        """Register callback for specific error category"""
        if category not in self.error_callbacks:
            self.error_callbacks[category] = []
        self.error_callbacks[category].append(callback)
    
    def _add_to_history(self, error_info: ErrorInfo):
        """Add error to history with size management"""
        self.error_history.append(error_info)
        
        # Maintain history size
        if len(self.error_history) > self.max_history_size:
            self.error_history = self.error_history[-self.max_history_size:]
    
    def _log_error(self, error_info: ErrorInfo):
        """Log error with appropriate level"""
        log_data = {
            "error_id": error_info.error_id,
            "category": error_info.category.value,
            "severity": error_info.severity.value,
            "message": error_info.message,
            "retryable": error_info.is_retryable,
            "retry_count": error_info.retry_count
        }
        
        if error_info.context:
            log_data.update({
                "provider": error_info.context.provider_name,
                "operation": error_info.context.operation,
                "request_id": error_info.context.request_id
            })
        
        # Log based on severity
        if error_info.severity == ErrorSeverity.CRITICAL:
            self.logger.critical("Critical error occurred", **log_data)
        elif error_info.severity == ErrorSeverity.HIGH:
            self.logger.error("High severity error", **log_data)
        elif error_info.severity == ErrorSeverity.MEDIUM:
            self.logger.warning("Medium severity error", **log_data)
        else:
            self.logger.info("Low severity error", **log_data)
    
    def _notify_callbacks(self, error_info: ErrorInfo):
        """Notify registered callbacks"""
        callbacks = self.error_callbacks.get(error_info.category, [])
        for callback in callbacks:
            try:
                callback(error_info)
            except Exception as e:
                self.logger.error(
                    "Error callback failed",
                    callback=callback.__name__,
                    error=str(e)
                )
    
    def get_error_statistics(self, hours: int = 24) -> Dict[str, Any]:
        """Get error statistics for the specified time period"""
        cutoff_time = datetime.now().replace(hour=datetime.now().hour - hours)
        recent_errors = [
            error for error in self.error_history
            if error.timestamp >= cutoff_time
        ]
        
        if not recent_errors:
            return {"total_errors": 0, "period_hours": hours}
        
        # Calculate statistics
        category_counts = {}
        severity_counts = {}
        provider_counts = {}
        retryable_count = 0
        
        for error in recent_errors:
            # Category counts
            category = error.category.value
            category_counts[category] = category_counts.get(category, 0) + 1
            
            # Severity counts
            severity = error.severity.value
            severity_counts[severity] = severity_counts.get(severity, 0) + 1
            
            # Provider counts
            if error.context and error.context.provider_name:
                provider = error.context.provider_name
                provider_counts[provider] = provider_counts.get(provider, 0) + 1
            
            # Retryable count
            if error.is_retryable:
                retryable_count += 1
        
        return {
            "total_errors": len(recent_errors),
            "period_hours": hours,
            "by_category": category_counts,
            "by_severity": severity_counts,
            "by_provider": provider_counts,
            "retryable_errors": retryable_count,
            "retry_rate": retryable_count / len(recent_errors) if recent_errors else 0,
            "error_rate_per_hour": len(recent_errors) / hours
        }
    
    def get_recent_errors(self, limit: int = 10, category: Optional[ErrorCategory] = None) -> List[ErrorInfo]:
        """Get recent errors, optionally filtered by category"""
        errors = self.error_history
        
        if category:
            errors = [error for error in errors if error.category == category]
        
        return sorted(errors, key=lambda x: x.timestamp, reverse=True)[:limit]
    
    def clear_history(self):
        """Clear error history"""
        self.error_history.clear()
        self.logger.info("Error history cleared")


# Global error handler instance
_error_handler_instance: Optional[ErrorHandler] = None


def get_error_handler() -> ErrorHandler:
    """Get global error handler instance"""
    global _error_handler_instance
    if _error_handler_instance is None:
        _error_handler_instance = ErrorHandler()
    return _error_handler_instance


def handle_error(exception: Exception, context: Optional[ErrorContext] = None) -> ErrorInfo:
    """Convenience function to handle errors"""
    return get_error_handler().handle_error(exception, context)


class ErrorHandlerDecorator:
    """Decorator for automatic error handling"""

    def __init__(self,
                 operation_name: Optional[str] = None,
                 provider_name: Optional[str] = None,
                 reraise: bool = True):
        self.operation_name = operation_name
        self.provider_name = provider_name
        self.reraise = reraise

    def __call__(self, func):
        async def async_wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                context = ErrorContext(
                    operation=self.operation_name or func.__name__,
                    provider_name=self.provider_name
                )

                error_info = handle_error(e, context)

                if self.reraise:
                    raise
                else:
                    return error_info

        def sync_wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                context = ErrorContext(
                    operation=self.operation_name or func.__name__,
                    provider_name=self.provider_name
                )

                error_info = handle_error(e, context)

                if self.reraise:
                    raise
                else:
                    return error_info

        # Return appropriate wrapper based on function type
        import asyncio
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper


def error_handler(operation_name: Optional[str] = None,
                 provider_name: Optional[str] = None,
                 reraise: bool = True):
    """Decorator for automatic error handling"""
    return ErrorHandlerDecorator(operation_name, provider_name, reraise)
