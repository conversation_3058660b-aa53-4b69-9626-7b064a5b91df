"""
Advanced Image Optimization for Vision API Calls
"""

import base64
import io
import math
from dataclasses import dataclass
from typing import Optional, <PERSON>ple

import structlog
from PIL import Image, ImageOps

logger = structlog.get_logger(__name__)


@dataclass
class CompressionSettings:
    """Image compression settings"""
    max_width: int = 1920
    max_height: int = 1080
    quality: int = 85
    format: str = "JPEG"  # JPEG for photos, PNG for screenshots
    progressive: bool = True
    optimize: bool = True
    auto_format: bool = True  # Automatically choose best format
    preserve_aspect_ratio: bool = True
    
    def __post_init__(self):
        """Validate settings"""
        if self.quality < 1 or self.quality > 100:
            raise ValueError("Quality must be between 1 and 100")
        if self.max_width < 1 or self.max_height < 1:
            raise ValueError("Dimensions must be positive")


class ImageOptimizer:
    """Advanced image optimizer for vision API calls"""
    
    def __init__(self, default_settings: Optional[CompressionSettings] = None):
        self.default_settings = default_settings or CompressionSettings()
        self.logger = logger.bind(component="image_optimizer")
    
    def optimize_image_base64(
        self,
        image_base64: str,
        settings: Optional[CompressionSettings] = None,
        target_size_kb: Optional[int] = None
    ) -> Tuple[str, dict]:
        """
        Optimize base64 image for API transmission
        
        Args:
            image_base64: Base64 encoded image
            settings: Compression settings
            target_size_kb: Target size in KB (will adjust quality to meet target)
            
        Returns:
            Tuple of (optimized_base64, optimization_stats)
        """
        settings = settings or self.default_settings
        
        try:
            # Decode base64 image
            image_data = base64.b64decode(image_base64)
            original_size = len(image_data)
            
            # Open image
            image = Image.open(io.BytesIO(image_data))
            original_format = image.format
            original_dimensions = image.size
            
            self.logger.debug(
                "Starting image optimization",
                original_size_kb=original_size / 1024,
                original_dimensions=original_dimensions,
                original_format=original_format
            )
            
            # Apply optimizations
            optimized_image = self._apply_optimizations(image, settings)
            
            # Determine output format
            output_format = self._determine_output_format(
                optimized_image, original_format, settings
            )
            
            # Compress with target size if specified
            if target_size_kb:
                optimized_base64, final_stats = self._compress_to_target_size(
                    optimized_image, output_format, target_size_kb, settings
                )
            else:
                optimized_base64, final_stats = self._compress_image(
                    optimized_image, output_format, settings
                )
            
            # Calculate optimization stats
            final_size = len(base64.b64decode(optimized_base64))
            compression_ratio = original_size / final_size if final_size > 0 else 1
            
            stats = {
                "original_size_kb": original_size / 1024,
                "optimized_size_kb": final_size / 1024,
                "compression_ratio": compression_ratio,
                "size_reduction_percent": ((original_size - final_size) / original_size) * 100,
                "original_dimensions": original_dimensions,
                "optimized_dimensions": optimized_image.size,
                "original_format": original_format,
                "output_format": output_format,
                **final_stats
            }
            
            self.logger.info(
                "Image optimization completed",
                **{k: v for k, v in stats.items() if k != "quality_iterations"}
            )
            
            return optimized_base64, stats
            
        except Exception as e:
            self.logger.error("Image optimization failed", error=str(e))
            # Return original image if optimization fails
            return image_base64, {"error": str(e), "optimized": False}
    
    def _apply_optimizations(self, image: Image.Image, settings: CompressionSettings) -> Image.Image:
        """Apply various image optimizations"""
        # Convert to RGB if necessary (for JPEG output)
        if image.mode in ('RGBA', 'LA', 'P'):
            if settings.format.upper() == 'JPEG':
                # Create white background for JPEG
                background = Image.new('RGB', image.size, (255, 255, 255))
                if image.mode == 'P':
                    image = image.convert('RGBA')
                background.paste(image, mask=image.split()[-1] if image.mode in ('RGBA', 'LA') else None)
                image = background
            else:
                image = image.convert('RGBA')
        elif image.mode != 'RGB' and settings.format.upper() == 'JPEG':
            image = image.convert('RGB')
        
        # Resize if necessary
        if (image.width > settings.max_width or image.height > settings.max_height):
            image = self._resize_image(image, settings.max_width, settings.max_height, settings.preserve_aspect_ratio)
        
        # Auto-rotate based on EXIF data
        try:
            image = ImageOps.exif_transpose(image)
        except Exception:
            pass  # Ignore EXIF errors
        
        return image
    
    def _resize_image(self, image: Image.Image, max_width: int, max_height: int, preserve_aspect: bool) -> Image.Image:
        """Resize image while optionally preserving aspect ratio"""
        if preserve_aspect:
            # Calculate scaling factor to fit within bounds
            scale_w = max_width / image.width
            scale_h = max_height / image.height
            scale = min(scale_w, scale_h, 1.0)  # Don't upscale
            
            new_width = int(image.width * scale)
            new_height = int(image.height * scale)
        else:
            new_width = min(image.width, max_width)
            new_height = min(image.height, max_height)
        
        if new_width != image.width or new_height != image.height:
            # Use high-quality resampling
            image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
        
        return image
    
    def _determine_output_format(self, image: Image.Image, original_format: str, settings: CompressionSettings) -> str:
        """Determine the best output format for the image"""
        if not settings.auto_format:
            return settings.format.upper()
        
        # Analyze image characteristics
        has_transparency = image.mode in ('RGBA', 'LA') or 'transparency' in image.info
        is_screenshot = self._is_likely_screenshot(image)
        
        # Decision logic
        if has_transparency:
            return "PNG"
        elif is_screenshot:
            return "PNG"  # Better for screenshots with text
        else:
            return "JPEG"  # Better for photos
    
    def _is_likely_screenshot(self, image: Image.Image) -> bool:
        """Heuristic to determine if image is likely a screenshot"""
        # Simple heuristic: screenshots often have limited colors and sharp edges
        try:
            # Convert to RGB for analysis
            if image.mode != 'RGB':
                analysis_image = image.convert('RGB')
            else:
                analysis_image = image
            
            # Sample pixels to estimate color diversity
            sample_size = min(1000, image.width * image.height)
            pixels = list(analysis_image.getdata())
            
            # Sample every nth pixel
            step = max(1, len(pixels) // sample_size)
            sampled_pixels = pixels[::step]
            
            # Count unique colors
            unique_colors = len(set(sampled_pixels))
            color_ratio = unique_colors / len(sampled_pixels)
            
            # Screenshots typically have lower color diversity
            return color_ratio < 0.1
            
        except Exception:
            return False
    
    def _compress_image(self, image: Image.Image, format: str, settings: CompressionSettings) -> Tuple[str, dict]:
        """Compress image with given settings"""
        output = io.BytesIO()
        
        save_kwargs = {
            'format': format,
            'optimize': settings.optimize
        }
        
        if format == 'JPEG':
            save_kwargs.update({
                'quality': settings.quality,
                'progressive': settings.progressive
            })
        elif format == 'PNG':
            save_kwargs.update({
                'compress_level': 6  # Good balance of speed vs compression
            })
        
        image.save(output, **save_kwargs)
        
        compressed_data = output.getvalue()
        compressed_base64 = base64.b64encode(compressed_data).decode('utf-8')
        
        stats = {
            "final_quality": settings.quality if format == 'JPEG' else 100,
            "quality_iterations": 1
        }
        
        return compressed_base64, stats
    
    def _compress_to_target_size(
        self, 
        image: Image.Image, 
        format: str, 
        target_size_kb: int,
        settings: CompressionSettings
    ) -> Tuple[str, dict]:
        """Compress image to target size by adjusting quality"""
        if format != 'JPEG':
            # Can't easily adjust PNG compression, use default
            return self._compress_image(image, format, settings)
        
        # Binary search for optimal quality
        min_quality = 10
        max_quality = settings.quality
        best_result = None
        iterations = 0
        max_iterations = 10
        
        target_size_bytes = target_size_kb * 1024
        
        while min_quality <= max_quality and iterations < max_iterations:
            iterations += 1
            current_quality = (min_quality + max_quality) // 2
            
            # Test compression with current quality
            output = io.BytesIO()
            test_settings = CompressionSettings(
                quality=current_quality,
                format=format,
                progressive=settings.progressive,
                optimize=settings.optimize
            )
            
            image.save(output, 
                      format=format,
                      quality=current_quality,
                      progressive=settings.progressive,
                      optimize=settings.optimize)
            
            compressed_data = output.getvalue()
            compressed_size = len(compressed_data)
            
            if compressed_size <= target_size_bytes:
                # Size is acceptable, try higher quality
                best_result = (compressed_data, current_quality)
                min_quality = current_quality + 1
            else:
                # Size too large, reduce quality
                max_quality = current_quality - 1
        
        if best_result:
            compressed_data, final_quality = best_result
            compressed_base64 = base64.b64encode(compressed_data).decode('utf-8')
            
            stats = {
                "final_quality": final_quality,
                "quality_iterations": iterations,
                "target_size_kb": target_size_kb,
                "achieved_size_kb": len(compressed_data) / 1024
            }
            
            return compressed_base64, stats
        else:
            # Fallback to minimum quality
            return self._compress_image(image, format, 
                                      CompressionSettings(quality=min_quality, format=format))
    
    def estimate_api_cost(self, image_base64: str, provider: str = "openai") -> dict:
        """Estimate API cost based on image size and provider"""
        try:
            image_data = base64.b64decode(image_base64)
            size_mb = len(image_data) / (1024 * 1024)
            
            # Rough cost estimates (as of 2024)
            cost_per_mb = {
                "openai": 0.01,  # Approximate
                "anthropic": 0.015,  # Approximate
                "google": 0.005,  # Approximate
                "custom": 0.0  # Local models
            }
            
            base_cost = cost_per_mb.get(provider.lower(), 0.01)
            estimated_cost = size_mb * base_cost
            
            return {
                "size_mb": size_mb,
                "estimated_cost_usd": estimated_cost,
                "provider": provider,
                "note": "Estimates are approximate and may vary"
            }
            
        except Exception as e:
            return {"error": str(e)}
    
    def get_optimization_recommendations(self, image_base64: str) -> dict:
        """Get optimization recommendations for an image"""
        try:
            image_data = base64.b64decode(image_base64)
            image = Image.open(io.BytesIO(image_data))
            
            size_kb = len(image_data) / 1024
            dimensions = image.size
            format = image.format
            
            recommendations = []
            
            # Size recommendations
            if size_kb > 1000:  # > 1MB
                recommendations.append({
                    "type": "size",
                    "message": f"Image is large ({size_kb:.1f}KB). Consider compression.",
                    "suggestion": "Use target_size_kb parameter or reduce quality"
                })
            
            # Dimension recommendations
            if dimensions[0] > 2048 or dimensions[1] > 2048:
                recommendations.append({
                    "type": "dimensions", 
                    "message": f"High resolution ({dimensions[0]}x{dimensions[1]}). May not improve analysis quality.",
                    "suggestion": "Resize to 1920x1080 or smaller"
                })
            
            # Format recommendations
            if format == 'PNG' and not self._is_likely_screenshot(image):
                recommendations.append({
                    "type": "format",
                    "message": "PNG format for photo-like image. JPEG may be more efficient.",
                    "suggestion": "Consider JPEG format with quality 85-95"
                })
            
            return {
                "current_size_kb": size_kb,
                "current_dimensions": dimensions,
                "current_format": format,
                "recommendations": recommendations
            }
            
        except Exception as e:
            return {"error": str(e)}
