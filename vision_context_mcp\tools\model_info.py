"""
Model Info Tool for Vision Context MCP Server
"""

from typing import Any, Dict, List, Union

from mcp.types import TextContent, ImageContent

from .base import BaseTool
from ..core.vision_llm import VisionLLM


class ModelInfoTool(BaseTool):
    """Tool for getting available model information"""
    
    def __init__(self):
        super().__init__(
            name="get_available_models",
            description="Mevcut Vision LLM modellerini ve bilgilerini listeler"
        )
        self.vision_llm = VisionLLM()
    
    async def execute(
        self, 
        arguments: Dict[str, Any]
    ) -> List[Union[TextContent, ImageContent]]:
        """
        Execute model info query
        
        Args:
            arguments: Tool arguments (no parameters required)
            
        Returns:
            Model information as content list
        """
        self._log_execution_start(arguments)
        
        try:
            # Get available models
            available_models = self.vision_llm.get_available_models()
            model_info = self.vision_llm.get_model_info()
            default_model = self.vision_llm.settings.default_vision_model
            
            if not available_models:
                return [self._create_text_content(
                    "❌ Hiçbir Vision LLM modeli mevcut değil.\n\n"
                    "Lütfen .env dosyasında en az bir API anahtarı yapılandırın veya "
                    "custom provider ayarlayın."
                )]
            
            # Format model information
            result_text = "🤖 Mevcut Vision LLM Modelleri:\n\n"
            result_text += f"⚙️ **Default Model**: {default_model}\n\n"

            for model_name in available_models:
                info = model_info.get(model_name, {})
                provider = info.get("provider", "Unknown")
                model = info.get("model", "Unknown")

                # Mark default model
                is_default = (default_model == model_name) or (default_model == "auto" and model_name == available_models[0])
                default_marker = " 🌟 (Default)" if is_default else ""

                result_text += f"**{model_name.upper()}{default_marker}**\n"
                result_text += f"  📡 Provider: {provider}\n"
                result_text += f"  🧠 Model: {model}\n"

                if model_name == "custom":
                    base_url = info.get("base_url", "Not configured")
                    result_text += f"  🔗 Base URL: {base_url}\n"

                result_text += f"  ✅ Status: Available\n\n"
            
            result_text += f"📊 Toplam {len(available_models)} model mevcut\n\n"
            result_text += "💡 **Kullanım:**\n"
            result_text += "Tool çağrılarında `preferred_model` parametresi ile "
            result_text += "istediğiniz modeli seçebilirsiniz:\n"
            result_text += "- `claude`: Anthropic Claude\n"
            result_text += "- `gpt4v`: OpenAI GPT-4V\n"
            result_text += "- `gemini`: Google Gemini\n"
            result_text += "- `custom`: Custom/Local provider\n\n"
            result_text += "Parametre belirtilmezse otomatik olarak mevcut "
            result_text += "modellerden biri seçilir."
            
            self._log_execution_end(success=True)
            
            return [self._create_text_content(result_text)]
            
        except Exception as e:
            self._log_execution_end(success=False, error=str(e))
            return [self._create_text_content(
                f"Model bilgisi alınırken hata oluştu: {str(e)}"
            )]
