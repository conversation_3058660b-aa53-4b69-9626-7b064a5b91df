"""
Model Info Tool for Vision Context MCP Server
"""

from typing import Any, Dict, List, Union

from mcp.types import TextContent, ImageContent

from .base import BaseTool
from ..core.enhanced_vision_llm import EnhancedVisionLLM


class ModelInfoTool(BaseTool):
    """Enhanced tool for getting comprehensive model information and system status"""

    def __init__(self):
        super().__init__(
            name="get_available_models",
            description="Get comprehensive information about available AI models and system status"
        )
        self.vision_llm = EnhancedVisionLLM()
    
    async def execute(
        self,
        arguments: Dict[str, Any]
    ) -> List[Union[TextContent, ImageContent]]:
        """
        Execute enhanced model info query

        Args:
            arguments: Tool arguments
                - include_metrics (optional): Include performance metrics
                - check_health (optional): Perform health check

        Returns:
            Comprehensive model information as content list
        """
        self._log_execution_start(arguments)

        try:
            include_metrics = arguments.get("include_metrics", True)
            check_health = arguments.get("check_health", False)

            # Initialize enhanced vision LLM if needed
            if not self.vision_llm._initialized:
                await self.vision_llm.initialize()

            # Get comprehensive system status
            system_status = await self.vision_llm.get_system_status()
            available_providers = await self.vision_llm.get_available_providers()

            if not available_providers:
                return [self._create_text_content(
                    "❌ No AI providers are available.\n\n"
                    "Please configure at least one API key in your .env file or "
                    "set up a custom provider."
                )]

            # Perform health check if requested
            health_status = None
            if check_health:
                health_status = await self.vision_llm.health_check()

            # Format comprehensive model information
            result_text = "🤖 Enhanced AI Model Information:\n\n"

            # System overview
            result_text += "📊 **System Status:**\n"
            result_text += f"• Initialized: {'✅' if system_status['initialized'] else '❌'}\n"
            result_text += f"• Available providers: {len(available_providers)}\n"

            # Performance settings
            settings = system_status.get('settings', {})
            result_text += f"• Image optimization: {'✅' if settings.get('optimization_enabled') else '❌'}\n"
            result_text += f"• Caching: {'✅' if settings.get('caching_enabled') else '❌'}\n"
            result_text += f"• Batching: {'✅' if settings.get('batching_enabled') else '❌'}\n\n"

            # Provider details
            result_text += "🔧 **Available Providers:**\n\n"

            for provider in available_providers:
                provider_info = provider
                status_icon = "✅" if provider_info['status'] == 'healthy' else "⚠️" if provider_info['status'] == 'degraded' else "❌"

                result_text += f"**{provider_info['name']} {status_icon}**\n"
                result_text += f"  📡 Type: {provider_info['type']}\n"
                result_text += f"  🎯 Priority: {provider_info['priority']}\n"
                result_text += f"  📊 Status: {provider_info['status']}\n"

                if include_metrics and 'metrics' in provider_info:
                    metrics = provider_info['metrics']
                    result_text += f"  📈 Success rate: {metrics.get('success_rate', 0):.1f}%\n"
                    result_text += f"  ⏱️ Avg response time: {metrics.get('average_response_time', 0):.2f}s\n"
                    result_text += f"  📊 Total requests: {metrics.get('total_requests', 0)}\n"

                if health_status and provider_info['name'] in health_status.get('providers', {}):
                    health_icon = "✅" if health_status['providers'][provider_info['name']] else "❌"
                    result_text += f"  🏥 Health check: {health_icon}\n"

                result_text += "\n"

            # Error statistics
            if include_metrics:
                error_stats = system_status.get('error_statistics', {})
                if error_stats.get('total_errors', 0) > 0:
                    result_text += "⚠️ **Error Statistics (24h):**\n"
                    result_text += f"• Total errors: {error_stats.get('total_errors', 0)}\n"
                    result_text += f"• Error rate: {error_stats.get('error_rate_per_hour', 0):.2f}/hour\n"
                    result_text += f"• Retry rate: {error_stats.get('retry_rate', 0):.1%}\n\n"

            # Usage instructions
            result_text += "💡 **Usage Instructions:**\n"
            result_text += "Use `preferred_model` parameter in tool calls to select a specific provider:\n"
            for provider in available_providers:
                result_text += f"• `{provider['name']}`: {provider['type']} provider\n"

            result_text += "\nIf no provider is specified, the system automatically selects the best available provider based on priority and health status."

            self._log_execution_end(success=True)

            return [self._create_text_content(result_text)]

        except Exception as e:
            self._log_execution_end(success=False, error=str(e))
            return [self._create_text_content(
                f"❌ Failed to get model information: {str(e)}\n\nPlease check your configuration and try again."
            )]
