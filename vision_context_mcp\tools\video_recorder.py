"""
Video Recorder Tool for Vision Context MCP Server
"""

import asyncio
import os
import tempfile
from typing import Any, Dict, List, Union

from mcp.types import TextContent, ImageContent

from .base import BaseTool
from ..core.screen_capture import ScreenCapture
from ..core.enhanced_vision_llm import EnhancedVisionLLM
from ..utils.helpers import encode_image_to_base64, get_timestamp


class VideoRecorderTool(BaseTool):
    """Tool for recording screen video"""
    
    def __init__(self):
        super().__init__(
            name="record_screen",
            description="Enhanced screen recording with AI analysis and optimization"
        )
        self.screen_capture = ScreenCapture()
        self.vision_llm = EnhancedVisionLLM()
    
    async def execute(
        self, 
        arguments: Dict[str, Any]
    ) -> List[Union[TextContent, ImageContent]]:
        """
        Execute screen recording
        
        Args:
            arguments: Tool arguments containing duration and optional mode
            
        Returns:
            Recording results and analysis
        """
        self._log_execution_start(arguments)
        
        try:
            # Validate arguments
            self._validate_arguments(arguments, ["duration"])
            
            duration = arguments["duration"]
            mode = arguments.get("mode", "fullscreen")
            analyze_content = arguments.get("analyze_content", True)
            preferred_model = arguments.get("preferred_model", None)

            # Initialize enhanced vision LLM if needed
            if analyze_content and not self.vision_llm._initialized:
                await self.vision_llm.initialize()

            # Validate duration
            if duration < 1 or duration > 300:
                raise ValueError("Recording duration must be between 1-300 seconds")
            
            # Create temporary file for video
            temp_dir = tempfile.gettempdir()
            timestamp = get_timestamp().replace(":", "-").replace(".", "-")
            video_filename = f"screen_recording_{timestamp}.mp4"
            video_path = os.path.join(temp_dir, video_filename)
            
            # Start recording
            self.logger.info("Starting screen recording", duration=duration, mode=mode)
            
            recording_task = asyncio.create_task(
                self.screen_capture.record_screen(
                    duration=duration,
                    output_path=video_path,
                    mode=mode
                )
            )
            
            # Wait for recording to complete
            await recording_task
            
            # Verify video file exists
            if not os.path.exists(video_path):
                raise FileNotFoundError("Video file could not be created")

            file_size = os.path.getsize(video_path)

            # Capture a frame from the video for analysis
            frame_image = await self.screen_capture.extract_frame_from_video(
                video_path,
                timestamp_sec=duration // 2  # Middle frame
            )

            analysis_result = ""
            analysis_metadata = {}

            if analyze_content and frame_image:
                frame_base64 = encode_image_to_base64(frame_image)
                analysis_result, analysis_metadata = await self.vision_llm.analyze_image(
                    frame_base64,
                    prompt=f"Analyze this frame from a {duration}-second screen recording. Describe what activity or content is shown.",
                    preferred_provider=preferred_model,
                    optimize_image=True,
                    use_cache=True
                )
            
            self._log_execution_end(success=True)

            # Prepare enhanced result with metadata
            provider_info = ""
            timing_info = ""
            optimization_info = ""

            if analysis_metadata:
                provider_info = f"Provider: {analysis_metadata.get('provider_used', 'Unknown')}"
                timing_info = f"Analysis time: {analysis_metadata.get('processing_time_seconds', 0):.2f}s"

                if analysis_metadata.get('optimization_applied'):
                    opt_stats = analysis_metadata.get('optimization_stats', {})
                    optimization_info = f"\n🔧 Frame optimized: {opt_stats.get('original_size_kb', 0):.1f}KB → {opt_stats.get('optimized_size_kb', 0):.1f}KB"

            result_text = f"""🎥 Enhanced Screen Recording Completed:

📁 File: {video_filename}
📍 Location: {video_path}
⏱️ Duration: {duration} seconds
📊 Size: {file_size / 1024 / 1024:.2f} MB
🎯 Mode: {mode}

📝 AI Analysis:
{analysis_result if analysis_result else 'Analysis not performed'}

📊 Analysis Metadata:
{f'• {provider_info}' if provider_info else ''}
{f'• {timing_info}' if timing_info else ''}
{f'• Analysis enabled: {analyze_content}' if not analysis_result else ''}{optimization_info}

💡 Note: Video file is stored in temporary directory."""
            
            content = [self._create_text_content(result_text)]
            
            # Add frame image if available
            if frame_image:
                frame_base64 = encode_image_to_base64(frame_image)
                content.append(self._create_image_content(
                    data=frame_base64,
                    mime_type="image/png"
                ))
            
            return content
            
        except Exception as e:
            self._log_execution_end(success=False, error=str(e))
            return [self._create_text_content(
                f"❌ Video recording failed: {str(e)}\n\nPlease check your configuration and try again."
            )]
