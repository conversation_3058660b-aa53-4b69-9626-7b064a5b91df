"""
Screen Analyzer Tool for Vision Context MCP Server
"""

from typing import Any, Dict, List, Union

from mcp.types import TextContent, ImageContent

from .base import BaseTool
from ..core.screen_capture import ScreenCapture
from ..core.enhanced_vision_llm import EnhancedVisionLLM
from ..utils.helpers import encode_image_to_base64


class ScreenAnalyzerTool(BaseTool):
    """Tool for analyzing screen content"""
    
    def __init__(self):
        super().__init__(
            name="analyze_screen_context",
            description="Enhanced screen analysis with AI model optimization and caching"
        )
        self.screen_capture = ScreenCapture()
        self.vision_llm = EnhancedVisionLLM()
    
    async def execute(
        self, 
        arguments: Dict[str, Any]
    ) -> List[Union[TextContent, ImageContent]]:
        """
        Execute screen analysis
        
        Args:
            arguments: Tool arguments containing mode and optional analysis_prompt
            
        Returns:
            Analysis results as content list
        """
        self._log_execution_start(arguments)
        
        try:
            # Validate arguments
            self._validate_arguments(arguments, ["mode"])
            
            mode = arguments["mode"]
            analysis_prompt = arguments.get(
                "analysis_prompt",
                "Analyze this screen content in detail. Describe what you see and identify any important elements."
            )
            preferred_model = arguments.get("preferred_model", None)
            optimize_image = arguments.get("optimize_image", True)
            target_size_kb = arguments.get("target_size_kb", None)
            use_cache = arguments.get("use_cache", True)

            # Initialize enhanced vision LLM if needed
            if not self.vision_llm._initialized:
                await self.vision_llm.initialize()

            # Capture screen
            if mode == "fullscreen":
                image = await self.screen_capture.capture_fullscreen()
            elif mode == "active_window":
                image = await self.screen_capture.capture_active_window()
            else:
                raise ValueError(f"Invalid mode: {mode}")

            if image is None:
                return [self._create_text_content(
                    "Failed to capture screen. Please try again."
                )]

            # Encode image to base64
            image_base64 = encode_image_to_base64(image)

            # Analyze with Enhanced Vision LLM
            analysis_result, metadata = await self.vision_llm.analyze_image(
                image_base64,
                prompt=analysis_prompt,
                preferred_provider=preferred_model,
                optimize_image=optimize_image,
                target_size_kb=target_size_kb,
                use_cache=use_cache
            )
            
            self._log_execution_end(success=True)

            # Prepare enhanced response with metadata
            provider_info = f"Provider: {metadata.get('provider_used', 'Unknown')}"
            timing_info = f"Processing time: {metadata.get('processing_time_seconds', 0):.2f}s"
            optimization_info = ""

            if metadata.get('optimization_applied'):
                opt_stats = metadata.get('optimization_stats', {})
                optimization_info = f"\n🔧 Image optimized: {opt_stats.get('original_size_kb', 0):.1f}KB → {opt_stats.get('optimized_size_kb', 0):.1f}KB"

            cache_info = "💾 Cache hit" if metadata.get('cache_hit') else "🆕 Fresh analysis"

            response_text = f"""📸 Enhanced Screen Analysis ({mode}):

{analysis_result}

📊 Analysis Metadata:
• {provider_info}
• {timing_info}
• {cache_info}{optimization_info}"""

            # Return both the analysis and the captured image
            return [
                self._create_text_content(response_text),
                self._create_image_content(
                    data=image_base64,
                    mime_type="image/png"
                )
            ]
            
        except Exception as e:
            self._log_execution_end(success=False, error=str(e))
            return [self._create_text_content(
                f"❌ Screen analysis failed: {str(e)}\n\nPlease check your configuration and try again."
            )]
