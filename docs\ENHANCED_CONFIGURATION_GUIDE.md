# 🚀 Enhanced AI Model Configuration Guide

This guide covers the comprehensive AI model integration system with advanced features for performance, reliability, and user experience.

## 🎯 Overview

The enhanced system provides:
- **Multi-Provider Support**: Anthropic Claude, OpenAI GPT-4V, Google Gemini, Custom OpenAI-compatible APIs
- **Performance Optimization**: Image compression, request batching, connection pooling, caching
- **Resilience Features**: Circuit breakers, retry mechanisms, fallback strategies
- **User-Friendly Configuration**: Validation, templates, runtime updates
- **Comprehensive Monitoring**: Health checks, metrics, error tracking

## 📋 Configuration Structure

### Basic Configuration Template

```yaml
# Provider Configurations
anthropic:
  enabled: true
  api_key: "your_anthropic_key_here"
  model_name: "claude-3-5-sonnet-20241022"
  max_tokens: 1000
  timeout: 30.0
  max_retries: 3
  priority: 1

openai:
  enabled: true
  api_key: "your_openai_key_here"
  model_name: "gpt-4-vision-preview"
  max_tokens: 1000
  timeout: 30.0
  max_retries: 3
  priority: 2

custom:
  enabled: true
  base_url: "http://localhost:11434/v1"
  model_name: "llava"
  api_key: "not-required"
  timeout: 45.0
  priority: 3

# Performance Optimization
optimization:
  enable_caching: true
  cache_ttl_seconds: 300
  max_cache_size: 1000
  enable_batching: true
  max_batch_size: 5
  max_batch_wait_time: 2.0
  enable_image_optimization: true
  max_image_width: 1920
  max_image_height: 1080
  image_quality: 85

# Security Settings
security:
  require_auth: false
  allowed_domains: ["localhost", "127.0.0.1"]
  max_request_size_mb: 20
  rate_limit_global: 1000

# Monitoring
monitoring:
  log_level: "INFO"
  enable_metrics: true
  health_check_interval: 60.0
  alert_on_provider_failure: true
```

## 🔧 Environment Variables

### Provider Configuration
```bash
# Anthropic Claude
VISION_MCP_ANTHROPIC_ENABLED=true
VISION_MCP_ANTHROPIC_API_KEY=your_anthropic_key_here
VISION_MCP_ANTHROPIC_MODEL_NAME=claude-3-5-sonnet-20241022
VISION_MCP_ANTHROPIC_MAX_TOKENS=1000
VISION_MCP_ANTHROPIC_TIMEOUT=30.0
VISION_MCP_ANTHROPIC_MAX_RETRIES=3
VISION_MCP_ANTHROPIC_PRIORITY=1

# OpenAI GPT-4V
VISION_MCP_OPENAI_ENABLED=true
VISION_MCP_OPENAI_API_KEY=your_openai_key_here
VISION_MCP_OPENAI_MODEL_NAME=gpt-4-vision-preview
VISION_MCP_OPENAI_MAX_TOKENS=1000
VISION_MCP_OPENAI_TIMEOUT=30.0
VISION_MCP_OPENAI_MAX_RETRIES=3
VISION_MCP_OPENAI_PRIORITY=2

# Custom Provider (Ollama example)
VISION_MCP_CUSTOM_ENABLED=true
VISION_MCP_CUSTOM_BASE_URL=http://localhost:11434/v1
VISION_MCP_CUSTOM_MODEL_NAME=llava
VISION_MCP_CUSTOM_API_KEY=not-required
VISION_MCP_CUSTOM_TIMEOUT=45.0
VISION_MCP_CUSTOM_PRIORITY=3

# Performance Optimization
VISION_MCP_OPTIMIZATION_ENABLE_CACHING=true
VISION_MCP_OPTIMIZATION_CACHE_TTL_SECONDS=300
VISION_MCP_OPTIMIZATION_MAX_CACHE_SIZE=1000
VISION_MCP_OPTIMIZATION_ENABLE_BATCHING=true
VISION_MCP_OPTIMIZATION_MAX_BATCH_SIZE=5
VISION_MCP_OPTIMIZATION_MAX_BATCH_WAIT_TIME=2.0
VISION_MCP_OPTIMIZATION_ENABLE_IMAGE_OPTIMIZATION=true
VISION_MCP_OPTIMIZATION_MAX_IMAGE_WIDTH=1920
VISION_MCP_OPTIMIZATION_MAX_IMAGE_HEIGHT=1080
VISION_MCP_OPTIMIZATION_IMAGE_QUALITY=85
```

## 🏗️ Configuration Examples

### 1. Cloud-Only Setup (Production)
```yaml
anthropic:
  enabled: true
  api_key: "${ANTHROPIC_API_KEY}"
  priority: 1
  max_retries: 5
  circuit_breaker_threshold: 3

openai:
  enabled: true
  api_key: "${OPENAI_API_KEY}"
  priority: 2
  max_retries: 5
  circuit_breaker_threshold: 3

optimization:
  enable_caching: true
  enable_batching: true
  enable_image_optimization: true
  image_quality: 90

security:
  require_auth: true
  api_key_encryption: true
  rate_limit_global: 500

monitoring:
  log_level: "INFO"
  enable_metrics: true
  alert_on_provider_failure: true
  alert_on_high_error_rate: true
```

### 2. Local-First Setup (Development)
```yaml
custom:
  enabled: true
  base_url: "http://localhost:11434/v1"
  model_name: "llava"
  priority: 1
  timeout: 60.0
  max_retries: 2

anthropic:
  enabled: true
  api_key: "${ANTHROPIC_API_KEY}"
  priority: 2

optimization:
  enable_caching: true
  cache_ttl_seconds: 600
  enable_image_optimization: true
  max_image_width: 1280
  max_image_height: 720
  image_quality: 75

monitoring:
  log_level: "DEBUG"
  enable_metrics: true
```

### 3. High-Performance Setup
```yaml
anthropic:
  enabled: true
  api_key: "${ANTHROPIC_API_KEY}"
  priority: 1
  rate_limit_requests: 100
  max_retries: 3

openai:
  enabled: true
  api_key: "${OPENAI_API_KEY}"
  priority: 2
  rate_limit_requests: 80
  max_retries: 3

optimization:
  enable_caching: true
  cache_ttl_seconds: 900
  max_cache_size: 5000
  enable_batching: true
  max_batch_size: 10
  max_batch_wait_time: 1.0
  enable_image_optimization: true
  enable_connection_pooling: true
  max_connections_per_provider: 20

monitoring:
  log_level: "INFO"
  enable_metrics: true
  metrics_retention_days: 30
```

## 🎛️ Advanced Configuration Options

### Provider-Specific Settings

#### Circuit Breaker Configuration
```yaml
anthropic:
  circuit_breaker_threshold: 5    # Failures before opening circuit
  circuit_breaker_timeout: 60.0   # Seconds before trying again
  health_check_interval: 300.0    # Health check frequency
```

#### Rate Limiting
```yaml
openai:
  rate_limit_requests: 60         # Requests per window
  rate_limit_window: 60           # Window size in seconds
```

#### Custom Headers
```yaml
custom:
  custom_headers:
    "User-Agent": "VisionMCP/1.0"
    "X-Custom-Header": "value"
```

### Image Optimization Settings
```yaml
optimization:
  enable_image_optimization: true
  max_image_width: 1920
  max_image_height: 1080
  image_quality: 85
  auto_format_selection: true     # Automatically choose PNG vs JPEG
```

### Caching Configuration
```yaml
optimization:
  enable_caching: true
  cache_ttl_seconds: 300          # Cache lifetime
  max_cache_size: 1000           # Maximum cached items
```

### Batching Configuration
```yaml
optimization:
  enable_batching: true
  max_batch_size: 5              # Maximum requests per batch
  max_batch_wait_time: 2.0       # Maximum wait time for batch
```

## 🔍 Configuration Validation

The system automatically validates configuration on startup:

```python
from vision_context_mcp.config.enhanced_settings import get_enhanced_settings

settings = get_enhanced_settings()
validation_result = settings.validate_configuration()

if not validation_result["valid"]:
    print("Configuration errors:", validation_result["errors"])
if validation_result["warnings"]:
    print("Configuration warnings:", validation_result["warnings"])
```

## 🚀 Runtime Configuration Updates

Update configuration without restart:

```python
from vision_context_mcp.config.enhanced_settings import get_enhanced_settings

settings = get_enhanced_settings()

# Update provider settings
settings.update_provider_config("anthropic", max_tokens=1500, timeout=45.0)

# Update optimization settings
settings.optimization.enable_caching = False
settings.optimization.max_batch_size = 8
```

## 📊 Configuration Templates

Generate configuration templates:

```python
from vision_context_mcp.config.enhanced_settings import ConfigurationManager

config_manager = ConfigurationManager()

# Generate basic template
basic_config = config_manager.create_template_config("basic")

# Generate local development template
local_config = config_manager.create_template_config("local")

# Generate production template
prod_config = config_manager.create_template_config("production")

# Generate .env file
config_manager.generate_env_file(basic_config, ".env.example")
```

## 🔧 Best Practices

### 1. Provider Priority
- Set higher priority (lower number) for faster/more reliable providers
- Use local providers as primary for development
- Use cloud providers as primary for production

### 2. Performance Optimization
- Enable caching for repeated requests
- Use image optimization to reduce API costs
- Enable batching for high-volume scenarios
- Adjust batch size based on your usage patterns

### 3. Error Handling
- Set appropriate retry counts (3-5 for production)
- Configure circuit breakers to prevent cascade failures
- Monitor error rates and adjust thresholds

### 4. Security
- Use environment variables for API keys
- Enable authentication for production deployments
- Set appropriate rate limits
- Restrict allowed domains

### 5. Monitoring
- Enable metrics collection
- Set up alerts for provider failures
- Monitor error rates and response times
- Use appropriate log levels (INFO for production, DEBUG for development)

## 🔍 Troubleshooting

### Common Issues

1. **No providers configured**
   - Ensure at least one provider has valid credentials
   - Check API key format and permissions

2. **High error rates**
   - Check provider status and rate limits
   - Verify network connectivity
   - Review circuit breaker settings

3. **Slow performance**
   - Enable image optimization
   - Increase cache size
   - Adjust batch settings
   - Check provider response times

4. **Memory usage**
   - Reduce cache size
   - Limit conversation history
   - Enable image optimization

### Configuration Validation
```bash
# Test configuration
python -c "
from vision_context_mcp.config.enhanced_settings import get_enhanced_settings
settings = get_enhanced_settings()
result = settings.validate_configuration()
print('Valid:', result['valid'])
print('Errors:', result['errors'])
print('Warnings:', result['warnings'])
"
```
