# Vision Context MCP Server Configuration

# Server Settings
VISION_MCP_SERVER_HOST=localhost
VISION_MCP_SERVER_PORT=8000
VISION_MCP_DEBUG=false

# Vision LLM API Keys
VISION_MCP_ANTHROPIC_API_KEY=your_anthropic_api_key_here
VISION_MCP_OPENAI_API_KEY=your_openai_api_key_here
VISION_MCP_GOOGLE_API_KEY=your_google_api_key_here

# Default Model Selection
# Options: auto, claude, gpt4v, gemini, custom
# If set to "auto", the system will automatically select the first available model
# Priority order: custom -> claude -> gpt4v -> gemini
VISION_MCP_DEFAULT_VISION_MODEL=auto

# Custom Provider Settings (OpenAI Compatible)
# Examples for popular local model servers:

# Ollama (default setup)
# VISION_MCP_CUSTOM_API_BASE_URL=http://localhost:11434/v1
# VISION_MCP_CUSTOM_API_KEY=not-required
# VISION_MCP_CUSTOM_MODEL_NAME=llava
# VISION_MCP_CUSTOM_PROVIDER_NAME=Ollama

# LocalAI
# VISION_MCP_CUSTOM_API_BASE_URL=http://localhost:8080/v1
# VISION_MCP_CUSTOM_API_KEY=not-required
# VISION_MCP_CUSTOM_MODEL_NAME=gpt-4-vision-preview
# VISION_MCP_CUSTOM_PROVIDER_NAME=LocalAI

# LM Studio
# VISION_MCP_CUSTOM_API_BASE_URL=http://localhost:1234/v1
# VISION_MCP_CUSTOM_API_KEY=not-required
# VISION_MCP_CUSTOM_MODEL_NAME=your-model-name
# VISION_MCP_CUSTOM_PROVIDER_NAME=LM Studio

# Custom OpenAI-compatible server
# VISION_MCP_CUSTOM_API_BASE_URL=http://your-server:port/v1
# VISION_MCP_CUSTOM_API_KEY=your-api-key-if-required
# VISION_MCP_CUSTOM_MODEL_NAME=your-model-name
# VISION_MCP_CUSTOM_PROVIDER_NAME=Custom Server

# Screen Capture Settings
VISION_MCP_CAPTURE_QUALITY=95
VISION_MCP_CAPTURE_FORMAT=PNG

# Video Recording Settings
VISION_MCP_VIDEO_FPS=30
VISION_MCP_VIDEO_QUALITY=23
VISION_MCP_MAX_RECORDING_DURATION=300

# Context Engine Settings
VISION_MCP_CONTEXT_CHECK_INTERVAL=1.0
VISION_MCP_MAX_CONTEXT_HISTORY=100

# Logging
VISION_MCP_LOG_LEVEL=INFO
VISION_MCP_LOG_FILE=vision_mcp.log

# Security
VISION_MCP_REQUIRE_AUTH=false
