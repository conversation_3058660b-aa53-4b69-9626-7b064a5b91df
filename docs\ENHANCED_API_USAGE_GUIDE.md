# 🔧 Enhanced API Usage Guide

This guide demonstrates how to use the enhanced Vision Context MCP Server with all its advanced features.

## 🚀 Quick Start

### Basic Usage
```python
from vision_context_mcp.core.enhanced_vision_llm import EnhancedVisionLLM

# Initialize the enhanced system
vision_llm = EnhancedVisionLLM()
await vision_llm.initialize()

# Analyze an image
result, metadata = await vision_llm.analyze_image(
    image_base64="your_base64_image_here",
    prompt="What do you see in this image?"
)

print(f"Analysis: {result}")
print(f"Provider used: {metadata['provider_used']}")
print(f"Processing time: {metadata['processing_time_seconds']:.2f}s")
```

## 🎯 Advanced Features

### 1. Image Optimization
```python
# Automatic optimization
result, metadata = await vision_llm.analyze_image(
    image_base64=large_image,
    prompt="Analyze this image",
    optimize_image=True,
    target_size_kb=500  # Target 500KB
)

# Check optimization results
if metadata["optimization_applied"]:
    stats = metadata["optimization_stats"]
    print(f"Original size: {stats['original_size_kb']:.1f}KB")
    print(f"Optimized size: {stats['optimized_size_kb']:.1f}KB")
    print(f"Compression ratio: {stats['compression_ratio']:.1f}x")
```

### 2. Provider Selection
```python
# Use specific provider
result, metadata = await vision_llm.analyze_image(
    image_base64=image,
    prompt="Analyze this",
    preferred_provider="Anthropic Claude"
)

# Get available providers
providers = await vision_llm.get_available_providers()
for provider in providers:
    print(f"{provider['name']}: {provider['status']} (Priority: {provider['priority']})")
```

### 3. Caching and Batching
```python
# Use caching (default: enabled)
result1, _ = await vision_llm.analyze_image(
    image_base64=image,
    prompt="What's in this image?",
    use_cache=True
)

# Same request will use cache
result2, metadata2 = await vision_llm.analyze_image(
    image_base64=image,
    prompt="What's in this image?",
    use_cache=True
)

print(f"Cache hit: {metadata2.get('cache_hit', False)}")

# Disable batching for urgent requests
result, _ = await vision_llm.analyze_image(
    image_base64=image,
    prompt="Emergency analysis",
    use_batching=False
)
```

### 4. Error Handling and Resilience
```python
try:
    result, metadata = await vision_llm.analyze_image(
        image_base64=image,
        prompt="Analyze this image"
    )
except Exception as e:
    # Enhanced error information is automatically logged
    print(f"Analysis failed: {e}")
    
    # Get recent errors
    recent_errors = vision_llm.error_handler.get_recent_errors(limit=5)
    for error in recent_errors:
        print(f"Error {error.error_id}: {error.category.value} - {error.message}")
```

## 📊 System Monitoring

### Health Checks
```python
# Comprehensive health check
health_status = await vision_llm.health_check()
print(f"System healthy: {health_status['healthy']}")
print(f"Healthy providers: {health_status['healthy_provider_count']}/{health_status['total_provider_count']}")

# Provider-specific health
for provider, status in health_status['providers'].items():
    print(f"{provider}: {'✅' if status else '❌'}")
```

### System Status
```python
# Get comprehensive system status
status = await vision_llm.get_system_status()

print("=== Provider Status ===")
for name, info in status['providers']['providers'].items():
    print(f"{name}: {info['status']} (Priority: {info['priority']})")
    metrics = info['metrics']
    print(f"  Success rate: {metrics['success_rate']:.1f}%")
    print(f"  Avg response time: {metrics['average_response_time']:.2f}s")

print("\n=== Error Statistics (24h) ===")
error_stats = status['error_statistics']
print(f"Total errors: {error_stats['total_errors']}")
print(f"Error rate: {error_stats['error_rate_per_hour']:.2f}/hour")

if status['batch_statistics']:
    print("\n=== Batch Statistics ===")
    batch_stats = status['batch_statistics']
    print(f"Batch efficiency: {batch_stats['batch_efficiency']:.1%}")
    print(f"Average batch size: {batch_stats['average_batch_size']:.1f}")
```

### Performance Metrics
```python
# Get conversation statistics
context = await vision_llm.get_conversation_context(limit=20)
print(f"Recent conversations: {len(context)}")

for msg in context[-5:]:  # Last 5 messages
    print(f"{msg['role']}: {msg['content'][:50]}...")
    if msg['provider_used']:
        print(f"  Provider: {msg['provider_used']}, Time: {msg['processing_time']:.2f}s")
```

## 🔧 Configuration Management

### Runtime Configuration Updates
```python
from vision_context_mcp.config.enhanced_settings import get_enhanced_settings

settings = get_enhanced_settings()

# Update provider settings
settings.update_provider_config("anthropic", 
    max_tokens=1500,
    timeout=45.0,
    priority=1
)

# Update optimization settings
settings.optimization.enable_caching = True
settings.optimization.max_batch_size = 8
settings.optimization.image_quality = 90

# Validate configuration
validation = settings.validate_configuration()
if validation['valid']:
    print("Configuration is valid")
else:
    print("Configuration errors:", validation['errors'])
```

### Export and Import Configuration
```python
# Export current configuration
config_dict = settings.export_config(include_secrets=False)

# Save to file
settings.save_to_file("current_config.json", include_secrets=False)

# Load from file
from vision_context_mcp.config.enhanced_settings import EnhancedSettings
new_settings = EnhancedSettings.load_from_file("current_config.json")
```

## 🎨 Image Optimization

### Get Optimization Recommendations
```python
# Get recommendations for an image
recommendations = await vision_llm.get_optimization_recommendations(image_base64)

print(f"Current size: {recommendations['current_size_kb']:.1f}KB")
print(f"Current dimensions: {recommendations['current_dimensions']}")
print(f"Current format: {recommendations['current_format']}")

for rec in recommendations['recommendations']:
    print(f"⚠️ {rec['type']}: {rec['message']}")
    print(f"   Suggestion: {rec['suggestion']}")
```

### Manual Image Optimization
```python
from vision_context_mcp.core.optimization import ImageOptimizer, CompressionSettings

optimizer = ImageOptimizer()

# Custom optimization settings
settings = CompressionSettings(
    max_width=1280,
    max_height=720,
    quality=80,
    format="JPEG",
    auto_format=True
)

# Optimize image
optimized_image, stats = optimizer.optimize_image_base64(
    image_base64,
    settings=settings,
    target_size_kb=300
)

print(f"Size reduction: {stats['size_reduction_percent']:.1f}%")
print(f"Compression ratio: {stats['compression_ratio']:.1f}x")
```

## 💰 Cost Estimation

### Estimate API Costs
```python
# Get cost estimates for all providers
cost_estimates = await vision_llm.estimate_api_costs(image_base64)

for provider, estimate in cost_estimates.items():
    if 'estimated_cost_usd' in estimate:
        print(f"{provider}: ${estimate['estimated_cost_usd']:.4f}")
        print(f"  Image size: {estimate['size_mb']:.2f}MB")
```

## 🔄 Conversation Management

### Conversation Context
```python
# Get conversation history
context = await vision_llm.get_conversation_context(limit=10)

for msg in context:
    print(f"[{msg['timestamp']}] {msg['role']}: {msg['content'][:100]}...")
    if msg['provider_used']:
        print(f"  Provider: {msg['provider_used']}")
        print(f"  Processing time: {msg['processing_time']:.2f}s")

# Clear conversation history
await vision_llm.clear_conversation_history()
```

### Model Information
```python
# Get detailed model information
model_info = vision_llm.get_model_info()

for provider, info in model_info.items():
    print(f"\n=== {provider} ===")
    print(f"Provider: {info['provider']}")
    print(f"Current model: {info['current_model']}")
    print(f"Capabilities: {', '.join(info['capabilities'])}")
    
    if 'supported_models' in info:
        print(f"Supported models: {', '.join(info['supported_models'])}")
```

## 🧪 Testing and Validation

### Test Provider Connectivity
```python
# Test all providers
health_results = await vision_llm.health_check()

for provider, healthy in health_results['providers'].items():
    status = "✅ Healthy" if healthy else "❌ Unhealthy"
    print(f"{provider}: {status}")

# Test with a simple image
test_image = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="

try:
    result, metadata = await vision_llm.analyze_image(
        image_base64=test_image,
        prompt="What do you see?",
        optimize_image=False
    )
    print(f"Test successful: {result[:50]}...")
except Exception as e:
    print(f"Test failed: {e}")
```

### Performance Testing
```python
import time
import asyncio

async def performance_test():
    """Test system performance with multiple concurrent requests"""
    
    # Prepare test data
    test_images = [test_image] * 10  # 10 identical images
    test_prompts = [f"Analyze image {i}" for i in range(10)]
    
    # Test concurrent requests
    start_time = time.time()
    
    tasks = []
    for i in range(10):
        task = vision_llm.analyze_image(
            image_base64=test_images[i],
            prompt=test_prompts[i],
            use_cache=True,
            use_batching=True
        )
        tasks.append(task)
    
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    end_time = time.time()
    total_time = end_time - start_time
    
    successful_requests = sum(1 for r in results if not isinstance(r, Exception))
    
    print(f"Performance Test Results:")
    print(f"Total time: {total_time:.2f}s")
    print(f"Successful requests: {successful_requests}/10")
    print(f"Average time per request: {total_time/10:.2f}s")
    print(f"Requests per second: {10/total_time:.2f}")

# Run performance test
await performance_test()
```

## 🧹 Cleanup

### Proper Cleanup
```python
# Always cleanup resources when done
try:
    # Your application logic here
    result, metadata = await vision_llm.analyze_image(image, prompt)
    
finally:
    # Cleanup all resources
    await vision_llm.cleanup()
```

## 🔍 Debugging

### Enable Debug Logging
```python
import logging
from vision_context_mcp.config.enhanced_settings import update_settings

# Enable debug logging
update_settings(monitoring={"log_level": "DEBUG"})

# Or set environment variable
import os
os.environ["VISION_MCP_MONITORING_LOG_LEVEL"] = "DEBUG"
```

### Error Analysis
```python
# Get detailed error statistics
error_stats = vision_llm.error_handler.get_error_statistics(hours=24)

print(f"Error breakdown by category:")
for category, count in error_stats['by_category'].items():
    print(f"  {category}: {count}")

print(f"Error breakdown by severity:")
for severity, count in error_stats['by_severity'].items():
    print(f"  {severity}: {count}")

# Get recent errors with details
recent_errors = vision_llm.error_handler.get_recent_errors(limit=5)
for error in recent_errors:
    print(f"\nError ID: {error.error_id}")
    print(f"Category: {error.category.value}")
    print(f"Severity: {error.severity.value}")
    print(f"Message: {error.message}")
    print(f"Retryable: {error.is_retryable}")
    print(f"Suggested action: {error.suggested_action}")
```
