"""
Request Batching System for Efficient API Usage
"""

import asyncio
import time
from dataclasses import dataclass, field
from typing import Any, Callable, Dict, List, Optional, Tuple
from uuid import uuid4

import structlog

logger = structlog.get_logger(__name__)


@dataclass
class BatchRequest:
    """Individual request in a batch"""
    id: str = field(default_factory=lambda: str(uuid4()))
    image_base64: str = ""
    prompt: str = ""
    kwargs: Dict[str, Any] = field(default_factory=dict)
    future: asyncio.Future = field(default_factory=asyncio.Future)
    timestamp: float = field(default_factory=time.time)
    priority: int = 1  # Higher number = higher priority
    
    def __post_init__(self):
        if not self.future:
            self.future = asyncio.Future()


@dataclass
class BatchConfig:
    """Configuration for request batching"""
    max_batch_size: int = 5
    max_wait_time: float = 2.0  # seconds
    enable_batching: bool = True
    priority_threshold: int = 10  # High priority requests bypass batching
    similar_request_threshold: float = 0.8  # Similarity threshold for deduplication


class RequestBatcher:
    """Intelligent request batcher for vision API calls"""
    
    def __init__(self, config: BatchConfig, executor_func: Callable):
        self.config = config
        self.executor_func = executor_func  # Function to execute individual requests
        self.pending_requests: List[BatchRequest] = []
        self.processing_lock = asyncio.Lock()
        self.batch_timer: Optional[asyncio.Task] = None
        self.logger = logger.bind(component="request_batcher")
        self._stats = {
            "total_requests": 0,
            "batched_requests": 0,
            "bypassed_requests": 0,
            "deduplicated_requests": 0,
            "average_batch_size": 0.0,
            "total_batches": 0
        }
    
    async def submit_request(
        self,
        image_base64: str,
        prompt: str,
        priority: int = 1,
        **kwargs
    ) -> str:
        """
        Submit a request for batched processing
        
        Args:
            image_base64: Base64 encoded image
            prompt: Analysis prompt
            priority: Request priority (higher = more urgent)
            **kwargs: Additional arguments
            
        Returns:
            Analysis result
        """
        self._stats["total_requests"] += 1
        
        # Check if batching is disabled or high priority
        if not self.config.enable_batching or priority >= self.config.priority_threshold:
            self._stats["bypassed_requests"] += 1
            self.logger.debug("Request bypassing batch", priority=priority)
            return await self.executor_func(image_base64, prompt, **kwargs)
        
        # Create batch request
        request = BatchRequest(
            image_base64=image_base64,
            prompt=prompt,
            kwargs=kwargs,
            priority=priority
        )
        
        # Check for similar pending requests (deduplication)
        similar_request = await self._find_similar_request(request)
        if similar_request:
            self._stats["deduplicated_requests"] += 1
            self.logger.debug("Request deduplicated", original_id=similar_request.id)
            # Wait for the similar request to complete
            return await similar_request.future
        
        # Add to pending requests
        async with self.processing_lock:
            self.pending_requests.append(request)
            self.pending_requests.sort(key=lambda x: (-x.priority, x.timestamp))
            
            # Start batch timer if not already running
            if self.batch_timer is None or self.batch_timer.done():
                self.batch_timer = asyncio.create_task(self._batch_timer())
            
            # Check if we should process immediately
            if len(self.pending_requests) >= self.config.max_batch_size:
                if not self.batch_timer.done():
                    self.batch_timer.cancel()
                asyncio.create_task(self._process_batch())
        
        # Wait for result
        return await request.future
    
    async def _find_similar_request(self, request: BatchRequest) -> Optional[BatchRequest]:
        """Find similar pending request for deduplication"""
        for pending in self.pending_requests:
            if self._calculate_similarity(request, pending) >= self.config.similar_request_threshold:
                return pending
        return None
    
    def _calculate_similarity(self, req1: BatchRequest, req2: BatchRequest) -> float:
        """Calculate similarity between two requests"""
        # Simple similarity based on prompt and image hash
        try:
            # Compare prompts
            prompt_similarity = self._text_similarity(req1.prompt, req2.prompt)
            
            # Compare image hashes (first 100 chars as simple hash)
            image_hash1 = req1.image_base64[:100] if len(req1.image_base64) > 100 else req1.image_base64
            image_hash2 = req2.image_base64[:100] if len(req2.image_base64) > 100 else req2.image_base64
            image_similarity = 1.0 if image_hash1 == image_hash2 else 0.0
            
            # Weighted average
            return (prompt_similarity * 0.3 + image_similarity * 0.7)
            
        except Exception:
            return 0.0
    
    def _text_similarity(self, text1: str, text2: str) -> float:
        """Simple text similarity calculation"""
        if text1 == text2:
            return 1.0
        
        # Simple word-based similarity
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())
        
        if not words1 and not words2:
            return 1.0
        if not words1 or not words2:
            return 0.0
        
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        return len(intersection) / len(union)
    
    async def _batch_timer(self):
        """Timer for batch processing"""
        try:
            await asyncio.sleep(self.config.max_wait_time)
            await self._process_batch()
        except asyncio.CancelledError:
            pass  # Timer was cancelled, batch will be processed immediately
    
    async def _process_batch(self):
        """Process current batch of requests"""
        async with self.processing_lock:
            if not self.pending_requests:
                return
            
            # Take requests for processing
            batch = self.pending_requests[:self.config.max_batch_size]
            self.pending_requests = self.pending_requests[self.config.max_batch_size:]
            
            self._stats["batched_requests"] += len(batch)
            self._stats["total_batches"] += 1
            self._stats["average_batch_size"] = (
                (self._stats["average_batch_size"] * (self._stats["total_batches"] - 1) + len(batch)) /
                self._stats["total_batches"]
            )
        
        self.logger.info("Processing batch", batch_size=len(batch))
        
        # Process requests concurrently
        tasks = []
        for request in batch:
            task = asyncio.create_task(self._execute_request(request))
            tasks.append(task)
        
        # Wait for all requests to complete
        await asyncio.gather(*tasks, return_exceptions=True)
    
    async def _execute_request(self, request: BatchRequest):
        """Execute individual request and set result"""
        try:
            start_time = time.time()
            result = await self.executor_func(
                request.image_base64,
                request.prompt,
                **request.kwargs
            )
            
            execution_time = time.time() - start_time
            self.logger.debug(
                "Request completed",
                request_id=request.id,
                execution_time=execution_time
            )
            
            request.future.set_result(result)
            
        except Exception as e:
            self.logger.error(
                "Request failed",
                request_id=request.id,
                error=str(e)
            )
            request.future.set_exception(e)
    
    async def flush_pending(self):
        """Flush all pending requests immediately"""
        async with self.processing_lock:
            if self.batch_timer and not self.batch_timer.done():
                self.batch_timer.cancel()
            
            if self.pending_requests:
                await self._process_batch()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get batching statistics"""
        stats = self._stats.copy()
        
        # Calculate additional metrics
        if stats["total_requests"] > 0:
            stats["batch_efficiency"] = stats["batched_requests"] / stats["total_requests"]
            stats["deduplication_rate"] = stats["deduplicated_requests"] / stats["total_requests"]
        else:
            stats["batch_efficiency"] = 0.0
            stats["deduplication_rate"] = 0.0
        
        stats["pending_requests"] = len(self.pending_requests)
        
        return stats
    
    async def cleanup(self):
        """Cleanup batcher resources"""
        # Cancel timer
        if self.batch_timer and not self.batch_timer.done():
            self.batch_timer.cancel()
        
        # Process any remaining requests
        await self.flush_pending()
        
        # Cancel any remaining futures
        for request in self.pending_requests:
            if not request.future.done():
                request.future.cancel()
        
        self.pending_requests.clear()
        self.logger.info("Request batcher cleaned up")


class SmartBatcher:
    """Smart batcher that adapts based on system load and performance"""
    
    def __init__(self, base_config: BatchConfig, executor_func: Callable):
        self.base_config = base_config
        self.executor_func = executor_func
        self.batcher = RequestBatcher(base_config, executor_func)
        self.performance_history: List[float] = []
        self.last_adjustment = time.time()
        self.adjustment_interval = 60.0  # Adjust every minute
        self.logger = logger.bind(component="smart_batcher")
    
    async def submit_request(self, image_base64: str, prompt: str, **kwargs) -> str:
        """Submit request with adaptive batching"""
        start_time = time.time()
        
        try:
            result = await self.batcher.submit_request(image_base64, prompt, **kwargs)
            
            # Record performance
            execution_time = time.time() - start_time
            self.performance_history.append(execution_time)
            
            # Keep only recent history
            if len(self.performance_history) > 100:
                self.performance_history = self.performance_history[-100:]
            
            # Adjust configuration if needed
            await self._maybe_adjust_config()
            
            return result
            
        except Exception as e:
            self.logger.error("Smart batcher request failed", error=str(e))
            raise
    
    async def _maybe_adjust_config(self):
        """Adjust batching configuration based on performance"""
        now = time.time()
        if now - self.last_adjustment < self.adjustment_interval:
            return
        
        if len(self.performance_history) < 10:
            return
        
        # Calculate average response time
        avg_response_time = sum(self.performance_history) / len(self.performance_history)
        
        # Adjust batch size based on performance
        current_config = self.batcher.config
        
        if avg_response_time > 10.0:  # Slow responses
            # Reduce batch size to improve responsiveness
            new_batch_size = max(1, current_config.max_batch_size - 1)
            new_wait_time = max(0.5, current_config.max_wait_time - 0.2)
        elif avg_response_time < 2.0:  # Fast responses
            # Increase batch size for better efficiency
            new_batch_size = min(10, current_config.max_batch_size + 1)
            new_wait_time = min(5.0, current_config.max_wait_time + 0.2)
        else:
            # Performance is acceptable, no changes
            return
        
        # Update configuration
        self.batcher.config.max_batch_size = new_batch_size
        self.batcher.config.max_wait_time = new_wait_time
        self.last_adjustment = now
        
        self.logger.info(
            "Adjusted batch configuration",
            new_batch_size=new_batch_size,
            new_wait_time=new_wait_time,
            avg_response_time=avg_response_time
        )
    
    def get_stats(self) -> Dict[str, Any]:
        """Get comprehensive batching statistics"""
        stats = self.batcher.get_stats()
        
        if self.performance_history:
            stats["performance"] = {
                "average_response_time": sum(self.performance_history) / len(self.performance_history),
                "min_response_time": min(self.performance_history),
                "max_response_time": max(self.performance_history),
                "samples": len(self.performance_history)
            }
        
        stats["current_config"] = {
            "max_batch_size": self.batcher.config.max_batch_size,
            "max_wait_time": self.batcher.config.max_wait_time,
            "enable_batching": self.batcher.config.enable_batching
        }
        
        return stats
    
    async def cleanup(self):
        """Cleanup smart batcher"""
        await self.batcher.cleanup()
