"""
Vision Query Tool for Vision Context MCP Server
"""

from typing import Any, Dict, List, Union

from mcp.types import Text<PERSON>ontent, ImageContent

from .base import BaseTool
from ..core.screen_capture import ScreenCapture
from ..core.enhanced_vision_llm import EnhancedVisionLLM
from ..utils.helpers import encode_image_to_base64


class VisionQueryTool(BaseTool):
    """Tool for querying vision LLM about current view"""
    
    def __init__(self):
        super().__init__(
            name="query_vision_about_current_view",
            description="Enhanced vision query with conversation context and optimization"
        )
        self.screen_capture = ScreenCapture()
        self.vision_llm = EnhancedVisionLLM()
    
    async def execute(
        self, 
        arguments: Dict[str, Any]
    ) -> List[Union[TextContent, ImageContent]]:
        """
        Execute vision query
        
        Args:
            arguments: Tool arguments containing question and optional context
            
        Returns:
            Query results as content list
        """
        self._log_execution_start(arguments)
        
        try:
            # Validate arguments
            self._validate_arguments(arguments, ["question"])
            
            question = arguments["question"]
            context = arguments.get("context", "")
            preferred_model = arguments.get("preferred_model", None)
            use_conversation_history = arguments.get("use_conversation_history", True)
            optimize_image = arguments.get("optimize_image", True)

            # Initialize enhanced vision LLM if needed
            if not self.vision_llm._initialized:
                await self.vision_llm.initialize()

            # Capture current screen (active window by default for queries)
            image = await self.screen_capture.capture_active_window()

            if image is None:
                # Fallback to fullscreen if active window capture fails
                image = await self.screen_capture.capture_fullscreen()

            if image is None:
                return [self._create_text_content(
                    "Failed to capture screen. Please try again."
                )]

            # Encode image to base64
            image_base64 = encode_image_to_base64(image)

            # Prepare enhanced prompt with context
            full_prompt = question
            if context:
                full_prompt = f"Context: {context}\n\nQuestion: {question}"

            # Add conversation history if enabled
            if use_conversation_history:
                conversation_context = await self.vision_llm.get_conversation_context(limit=5)
                if conversation_context:
                    history_summary = "Recent conversation:\n"
                    for msg in conversation_context[-3:]:  # Last 3 messages
                        history_summary += f"- {msg['role']}: {msg['content'][:100]}...\n"
                    full_prompt = f"{history_summary}\n{full_prompt}"

            # Query Enhanced Vision LLM
            response, metadata = await self.vision_llm.analyze_image(
                image_base64,
                prompt=full_prompt,
                preferred_provider=preferred_model,
                optimize_image=optimize_image,
                use_cache=True
            )

            # Get conversation context for display
            conversation_context = await self.vision_llm.get_conversation_context()
            
            self._log_execution_end(success=True)

            # Prepare enhanced response with metadata
            provider_info = f"Provider: {metadata.get('provider_used', 'Unknown')}"
            timing_info = f"Processing time: {metadata.get('processing_time_seconds', 0):.2f}s"
            cache_info = "💾 Cache hit" if metadata.get('cache_hit') else "🆕 Fresh analysis"

            optimization_info = ""
            if metadata.get('optimization_applied'):
                opt_stats = metadata.get('optimization_stats', {})
                optimization_info = f"\n🔧 Image optimized: {opt_stats.get('original_size_kb', 0):.1f}KB → {opt_stats.get('optimized_size_kb', 0):.1f}KB"

            result_text = f"""🤔 Enhanced Vision Query:

❓ Question: {question}
{f'📝 Context: {context}' if context else ''}

🤖 Response:
{response}

� Analysis Metadata:
• {provider_info}
• {timing_info}
• {cache_info}{optimization_info}
• 💭 Conversation history: {len(conversation_context)} messages"""

            return [
                self._create_text_content(result_text),
                self._create_image_content(
                    data=image_base64,
                    mime_type="image/png"
                )
            ]
            
        except Exception as e:
            self._log_execution_end(success=False, error=str(e))
            return [self._create_text_content(
                f"❌ Vision query failed: {str(e)}\n\nPlease check your configuration and try again."
            )]
