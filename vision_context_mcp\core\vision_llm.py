"""
Vision LLM Integration for Vision Context MCP Server
"""

import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime

import structlog
from anthropic import Async<PERSON>nthropic
from openai import AsyncOpenAI

try:
    import google.generativeai as genai
except ImportError:
    genai = None

from ..config.settings import get_settings

logger = structlog.get_logger(__name__)


class ConversationMessage:
    """Conversation message structure"""
    
    def __init__(self, role: str, content: str, image_data: Optional[str] = None, timestamp: Optional[datetime] = None):
        self.role = role
        self.content = content
        self.image_data = image_data
        self.timestamp = timestamp or datetime.now()


class VisionLLM:
    """Vision LLM integration for image analysis"""
    
    def __init__(self):
        self.settings = get_settings()
        self.conversation_history: List[ConversationMessage] = []
        
        # Initialize clients
        self.anthropic_client = None
        self.openai_client = None
        self.gemini_model = None
        self.custom_client = None

        self._initialize_clients()
    
    def _initialize_clients(self) -> None:
        """Initialize LLM clients based on available API keys"""
        
        # Initialize Anthropic (Claude)
        if self.settings.anthropic_api_key:
            try:
                self.anthropic_client = AsyncAnthropic(
                    api_key=self.settings.anthropic_api_key
                )
                logger.info("Anthropic client initialized")
            except Exception as e:
                logger.error("Failed to initialize Anthropic client", error=str(e))
        
        # Initialize OpenAI (GPT-4V)
        if self.settings.openai_api_key:
            try:
                self.openai_client = AsyncOpenAI(
                    api_key=self.settings.openai_api_key
                )
                logger.info("OpenAI client initialized")
            except Exception as e:
                logger.error("Failed to initialize OpenAI client", error=str(e))
        
        # Initialize Google Gemini
        if self.settings.google_api_key and genai:
            try:
                genai.configure(api_key=self.settings.google_api_key)
                self.gemini_model = genai.GenerativeModel('gemini-pro-vision')
                logger.info("Gemini client initialized")
            except Exception as e:
                logger.error("Failed to initialize Gemini client", error=str(e))

        # Initialize Custom Provider (OpenAI Compatible)
        if self.settings.custom_api_base_url:
            try:
                self.custom_client = AsyncOpenAI(
                    api_key=self.settings.custom_api_key or "not-required",
                    base_url=self.settings.custom_api_base_url
                )
                logger.info("Custom provider initialized",
                           provider=self.settings.custom_provider_name,
                           base_url=self.settings.custom_api_base_url,
                           model=self.settings.custom_model_name)
            except Exception as e:
                logger.error("Failed to initialize custom provider", error=str(e))
    
    async def analyze_image(
        self, 
        image_base64: str, 
        prompt: str = "Bu görüntüde ne görüyorsun? Detaylı analiz yap.",
        preferred_model: Optional[str] = None
    ) -> str:
        """
        Analyze image using available Vision LLM
        
        Args:
            image_base64: Base64 encoded image
            prompt: Analysis prompt
            preferred_model: Preferred model ("claude", "gpt4v", "gemini")
            
        Returns:
            Analysis result text
        """
        logger.info("Starting image analysis", prompt_length=len(prompt))
        
        # Determine which model to use
        if preferred_model == "claude" and self.anthropic_client:
            result = await self._analyze_with_claude(image_base64, prompt)
        elif preferred_model == "gpt4v" and self.openai_client:
            result = await self._analyze_with_gpt4v(image_base64, prompt)
        elif preferred_model == "gemini" and self.gemini_model:
            result = await self._analyze_with_gemini(image_base64, prompt)
        elif preferred_model == "custom" and self.custom_client:
            result = await self._analyze_with_custom(image_base64, prompt)
        else:
            # Use default model or auto-select
            default_model = self.settings.default_vision_model

            if default_model == "claude" and self.anthropic_client:
                result = await self._analyze_with_claude(image_base64, prompt)
            elif default_model == "gpt4v" and self.openai_client:
                result = await self._analyze_with_gpt4v(image_base64, prompt)
            elif default_model == "gemini" and self.gemini_model:
                result = await self._analyze_with_gemini(image_base64, prompt)
            elif default_model == "custom" and self.custom_client:
                result = await self._analyze_with_custom(image_base64, prompt)
            else:
                # Auto-select available model (prioritize based on configuration order)
                if self.custom_client:
                    result = await self._analyze_with_custom(image_base64, prompt)
                elif self.anthropic_client:
                    result = await self._analyze_with_claude(image_base64, prompt)
                elif self.openai_client:
                    result = await self._analyze_with_gpt4v(image_base64, prompt)
                elif self.gemini_model:
                    result = await self._analyze_with_gemini(image_base64, prompt)
                else:
                    raise ValueError("No Vision LLM client available. Please configure API keys or custom provider.")
        
        # Add to conversation history
        self._add_to_conversation("user", prompt, image_base64)
        self._add_to_conversation("assistant", result)
        
        logger.info("Image analysis completed", result_length=len(result))
        return result
    
    async def _analyze_with_claude(self, image_base64: str, prompt: str) -> str:
        """Analyze image with Claude Vision"""
        try:
            message = await self.anthropic_client.messages.create(
                model="claude-3-5-sonnet-20241022",
                max_tokens=1000,
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "image",
                                "source": {
                                    "type": "base64",
                                    "media_type": "image/png",
                                    "data": image_base64
                                }
                            },
                            {
                                "type": "text",
                                "text": prompt
                            }
                        ]
                    }
                ]
            )
            
            return message.content[0].text
            
        except Exception as e:
            logger.error("Claude analysis failed", error=str(e))
            raise
    
    async def _analyze_with_gpt4v(self, image_base64: str, prompt: str) -> str:
        """Analyze image with GPT-4V"""
        try:
            response = await self.openai_client.chat.completions.create(
                model="gpt-4-vision-preview",
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": prompt
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/png;base64,{image_base64}"
                                }
                            }
                        ]
                    }
                ],
                max_tokens=1000
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logger.error("GPT-4V analysis failed", error=str(e))
            raise
    
    async def _analyze_with_gemini(self, image_base64: str, prompt: str) -> str:
        """Analyze image with Gemini Pro Vision"""
        try:
            import base64
            from PIL import Image
            import io
            
            # Convert base64 to PIL Image
            image_data = base64.b64decode(image_base64)
            image = Image.open(io.BytesIO(image_data))
            
            # Generate content
            response = await asyncio.to_thread(
                self.gemini_model.generate_content,
                [prompt, image]
            )
            
            return response.text
            
        except Exception as e:
            logger.error("Gemini analysis failed", error=str(e))
            raise

    async def _analyze_with_custom(self, image_base64: str, prompt: str) -> str:
        """Analyze image with Custom Provider (OpenAI Compatible)"""
        try:
            response = await self.custom_client.chat.completions.create(
                model=self.settings.custom_model_name,
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": prompt
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/png;base64,{image_base64}"
                                }
                            }
                        ]
                    }
                ],
                max_tokens=1000
            )

            return response.choices[0].message.content

        except Exception as e:
            logger.error("Custom provider analysis failed",
                        provider=self.settings.custom_provider_name,
                        model=self.settings.custom_model_name,
                        error=str(e))
            raise
    
    def _add_to_conversation(
        self, 
        role: str, 
        content: str, 
        image_data: Optional[str] = None
    ) -> None:
        """Add message to conversation history"""
        message = ConversationMessage(role, content, image_data)
        self.conversation_history.append(message)
        
        # Limit conversation history
        max_history = self.settings.max_context_history
        if len(self.conversation_history) > max_history:
            self.conversation_history = self.conversation_history[-max_history:]
    
    async def get_conversation_context(self) -> List[Dict[str, Any]]:
        """Get conversation context for follow-up queries"""
        return [
            {
                "role": msg.role,
                "content": msg.content,
                "has_image": msg.image_data is not None,
                "timestamp": msg.timestamp.isoformat()
            }
            for msg in self.conversation_history
        ]
    
    def clear_conversation(self) -> None:
        """Clear conversation history"""
        self.conversation_history.clear()
        logger.info("Conversation history cleared")
    
    def get_available_models(self) -> List[str]:
        """Get list of available models"""
        models = []
        if self.custom_client:
            models.append("custom")
        if self.anthropic_client:
            models.append("claude")
        if self.openai_client:
            models.append("gpt4v")
        if self.gemini_model:
            models.append("gemini")
        return models

    def get_model_info(self) -> Dict[str, Any]:
        """Get detailed model information"""
        info = {}

        if self.custom_client:
            info["custom"] = {
                "provider": self.settings.custom_provider_name,
                "model": self.settings.custom_model_name,
                "base_url": self.settings.custom_api_base_url,
                "available": True
            }

        if self.anthropic_client:
            info["claude"] = {
                "provider": "Anthropic",
                "model": "claude-3-5-sonnet-20241022",
                "available": True
            }

        if self.openai_client:
            info["gpt4v"] = {
                "provider": "OpenAI",
                "model": "gpt-4-vision-preview",
                "available": True
            }

        if self.gemini_model:
            info["gemini"] = {
                "provider": "Google",
                "model": "gemini-pro-vision",
                "available": True
            }

        return info
